#!/bin/bash
ROOTDIR=$(cd $(dirname $BASH_SOURCE)/../..; pwd)
SCRIPTDIR=$(cd $(dirname $BASH_SOURCE); pwd)
source $SCRIPTDIR/../common/utils.sh

# Create KMS key
${SCRIPTDIR}/_1a_generate_kms_key.sh 

# Select network from "hardhat list-networks"
if [ -n "$1" ]; then
    CHOICE=$1
else
    # Hardhatからネットワーク一覧を取得
    DIRECTORIES=$(npx hardhat list-networks)
    menu "Select the network to deploy:" "$DIRECTORIES"
    # menu 関数後の $CHOICE を確認
    if [ -z "$CHOICE" ]; then
        message "err" "Invalid selection. Please try again."
        exit 1
    fi
fi

# set NETWORK
export NETWORK=$CHOICE
message "info" "Using provided network: $NETWORK"

# Error handling function
handle_error() {
    message "err" "An unexpected error occurred. Exiting."
    exit 1
}
trap 'handle_error' ERR

# Load local AWS_PROFILE
source $SCRIPTDIR/_load_aws_prof.sh
source $SCRIPTDIR/env/."$AWS_PROFILE"

# Check if AWS_PROFILE is set
if [ -z "$AWS_PROFILE" ]; then
    message "err" "AWS_PROFILE is not set. Please set AWS_PROFILE using 'awsp' and try again."
    exit 1
fi

# Display the AWS_PROFILE and confirm deployment
message "info" "AWS_PROFILE is set to \"$AWS_PROFILE\"."
choice "Do you want to proceed with the deployment using this \"$AWS_PROFILE\"?"

# Enter deployment target
message "info" "please enter the deployment target? (1:main,2:ibc,3:both)"
read target

if [ "$target" == "1" ]; then
    message "info" "deployment target contract-main"
elif [ "$target" == "2" ]; then
    message "info" "deployment target contract-ibc"
elif [ "$target" == "3" ]; then
    message "info" "deployment target contract-main, contract-ibc"
else
    message "info" "please enter the deployment target? (1:main,2:ibc,3:both)"
fi

# if $ZONE_ID is 3000, enter the number of business zone to register
if [ "$ZONE_ID" == "3000" ]; then
    message "info" "how many biz zone do you want to register?"
    read number_of_bizzone
    if ! [[ "$number_of_bizzone" =~ ^[0-9]+$ ]] || [ "$number_of_bizzone" -lt 1 ] || [ "$number_of_bizzone" -gt 999 ]; then
        message "err" "please enter a number between 1 and 999"
        exit 1
    fi

    # 3001~3001+bizzone_numの範囲のBizZoneを登録する旨を表示
    message "info" "registering BizZone from 3001 to $((3000 + number_of_bizzone))"

    export NUMBER_OF_BIZZONE=$number_of_bizzone
fi

# Clean the project before deployment
$ROOTDIR/scripts/clean.sh $NETWORK

# Ask the user if they want to restore from backup before deployment
while true; do
    prompt=$(message "q" "Do you want to restore from backup before deployment? (y/N): ")
    read -p "$prompt" restore_choice
    if [[ "$restore_choice" =~ ^[Yy]$ ]]; then
        # Restore from backup
        ${SCRIPTDIR}/_7a_restore_api_files.sh ${NETWORK}
        break
    elif [[ "$restore_choice" =~ ^[Nn]$ ]]; then
        # Generate env file for new deployment
        ${SCRIPTDIR}/_1_generate_env.sh ${NETWORK}
        break
    else
        message "w" "Invalid choice. Please enter 'y' or 'n'."
    fi
done

# 2.ポートフォワード接続する (only main network)
${SCRIPTDIR}/_2_port_forward.sh ${NETWORK}

# contract-mainのみをデプロイ実行する
if [ "$target" == "1" ]; then
  # 3.Migrationを実行する
  ${SCRIPTDIR}/_3_migrate_main.sh
  # 4.デプロイされたコントラクトを確認する
  ${SCRIPTDIR}/_4_deployed_confirmation.sh main
fi

# contract-ibcのみをデプロイ実行する
if [ "$target" == "2" ]; then
  # 3.Migrationを実行する
  ${SCRIPTDIR}/_3_migrate_ibc.sh
  # 4.デプロイされたコントラクトを確認する
  ${SCRIPTDIR}/_4_deployed_confirmation.sh ibc
  # 5.EscrowAccountを追加する
  ${SCRIPTDIR}/_5_register_escrow_account.sh ${NETWORK}
fi

# contract-main、contract-ibcをデプロイ実行する
if [ "$target" == "3" ]; then
  # 3.contract-mainのMigrationを実行する
  ${SCRIPTDIR}/_3_migrate_main.sh
  # 4.デプロイされたコントラクトを確認する
  ${SCRIPTDIR}/_4_deployed_confirmation.sh main
  # contract-ibcのデプロイ準備
  mkdir -p $ROOTDIR/s3-restore/deployments/${NETWORK}
  cp $ROOTDIR/deployments/${NETWORK}/IBCToken.json $ROOTDIR/s3-restore/deployments/${NETWORK}/IBCToken.json
  cp $ROOTDIR/deployments/${NETWORK}/Validator.json $ROOTDIR/s3-restore/deployments/${NETWORK}/Validator.json
  cp $ROOTDIR/deployments/${NETWORK}/Account.json $ROOTDIR/s3-restore/deployments/${NETWORK}/Account.json
  cp $ROOTDIR/deployments/${NETWORK}/AccessCtrl.json $ROOTDIR/s3-restore/deployments/${NETWORK}/AccessCtrl.json
  cp $ROOTDIR/deployments/${NETWORK}/BusinessZoneAccount.json $ROOTDIR/s3-restore/deployments/${NETWORK}/BusinessZoneAccount.json
  cp $ROOTDIR/deployments/${NETWORK}/Provider.json $ROOTDIR/s3-restore/deployments/${NETWORK}/Provider.json
  # 3.contract-ibcのMigrationを実行する
  ${SCRIPTDIR}/_3_migrate_ibc.sh
  ${SCRIPTDIR}/_3a_set_ibc_app.sh
  # 4.デプロイされたコントラクトを確認する
  ${SCRIPTDIR}/_4_deployed_confirmation.sh ibc
  # 5.EscrowAccountをBizZoneの数だけ追加する
  ${SCRIPTDIR}/_5_register_escrow_account.sh ${NETWORK} ${NUMBER_OF_BIZZONE}
fi

# 6. ポートフォワードを切断する (only main network)
${SCRIPTDIR}/_6_disconnect_port_forward.sh ${NETWORK}

# 7.コントラクトのバックアップファイルをアップロードする
${SCRIPTDIR}/_7_backup_abi_files.sh ${NETWORK}

# 8.ABIファイルをバックアップする
${SCRIPTDIR}/_8_upload_abi_files.sh ${NETWORK}

if [ "$target" == "1" ] || [ "$target" == "3" ]; then
  # 9.FINのABIファイルの一部をBIZにアップロードする（FIN実行の場合はスキップ）
  ${SCRIPTDIR}/_9_upload_fin_abi_to_biz.sh ${NETWORK}
fi

message "success" "Deployment process completed successfully."
