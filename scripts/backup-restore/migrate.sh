#!/bin/bash

# Exit on error
set -e

# Read command line arguments
NETWORK=$1
# Color codes for output
RED='\033[0;31m'
GREEN='\033[0;32m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Validate input parameters
if [ -z "$NETWORK" ]; then
    echo -e "${RED}Error: Missing required parameters${NC} "
    echo "Usage: $0 <network>"
    echo "Example: $0 localBiz"
    exit 1
fi

# Validate network exists
NETWORK_PATH="scripts/backup-restore/backupfiles/$NETWORK"
if [ ! -d "$NETWORK_PATH" ]; then
    echo -e "${RED}Error: Network directory '$NETWORK' not found${NC}"
    echo "Available networks:"
    ls scripts/backup-restore/backupfiles/
    exit 1
fi

echo -e "${BLUE}Starting migration process...${NC}"
echo "Network: $NETWORK"

# Build command with optional origin path
CMD="npx ts-node scripts/backup-restore/migration/index.ts \"$NETWORK\""
# Run migration script
if eval $CMD; then
    echo -e "${GREEN}Migration completed successfully${NC}"
else
    echo -e "${RED}Migration failed${NC}"
    exit 1
fi
