{"extends": "solhint:recommended", "plugins": ["dcf"], "rules": {"quotes": ["error", "double"], "compiler-version": ["error", "v0.8.10"], "dcf/no-comment-format": "warn", "dcf/one-contract-one-file": "error", "no-unused-vars": "warn", "dcf/interface-no-struct": "error", "dcf/no-console-log": "error", "avoid-low-level-calls": "error", "avoid-call-value": "error", "avoid-sha3": "error", "avoid-suicide": "error", "avoid-throw": "error", "avoid-tx-origin": "error", "check-send-result": "error", "state-visibility": "warn", "multiple-sends": "error", "no-complex-fallback": "error", "not-rely-on-block-hash": "error", "const-name-snakecase": "warn", "contract-name-camelcase": "error", "event-name-camelcase": "error", "private-vars-leading-underscore": "warn", "gas-custom-errors": "off", "no-global-import": "warn", "var-name-mixedcase": "warn", "func-name-mixedcase": "warn"}}