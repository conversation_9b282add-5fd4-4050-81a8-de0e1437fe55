import { Network } from '../helpers/constants'
import { SetBizZoneTerminatedArguments } from '../helpers/task-arguments'
import { LocalFinConfig } from '../helpers/types'
import { BaseTask } from './base-task'

export class SetBizZoneTerminatedTask extends BaseTask<SetBizZoneTerminatedArguments> {
  filePath = __filename

  constructor(network: Network) {
    super(network, 'setBizZoneTerminated')
  }

  protected getDefaultArguments(): SetBizZoneTerminatedArguments {
    const networkConfig = this.getNetworkConfig()
    return {
      accountId: networkConfig.ACCOUNT_ID_1,
      zoneId: (networkConfig as LocalFinConfig).BIZ_ZONE_ID,
    }
  }
}
