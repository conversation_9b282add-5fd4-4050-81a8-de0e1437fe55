import lodash from 'lodash'
import { Network, networkConfig } from '../helpers/constants'
import { LimitValues } from '../helpers/task-arguments'
import { CumulativeAmount } from '../helpers/types'
import { BurnTokenTask } from '../tasks/BurnTokenTask'
import { CheckBurnTask } from '../tasks/CheckBurnTask'
import { CheckExchangeTask } from '../tasks/CheckExchangeTask'
import { CheckMintTask } from '../tasks/CheckMintTask'
import { CheckTransactionTask } from '../tasks/CheckTransactionTask'
import { CumulativeResetTask } from '../tasks/CumulativeResetTask'
import { GetAccountAllTask } from '../tasks/GetAccountAllTask'
import { GetAccountInfoTask } from '../tasks/GetAccountInfoTask'
import { GetAccountLimitDataTask } from '../tasks/GetAccountLimitDataTask'
import { MintTokenTask } from '../tasks/MintTokenTask'
import { ModTokenLimitTask } from '../tasks/ModTokenLimitTask'
import { RegisterAccTask } from '../tasks/RegisterAccTask'
import { SetActiveBusinessAccountWithZoneTask } from '../tasks/SetActiveBusinessAccountWithZoneTask'
import { SyncAccountTask } from '../tasks/SyncAccountTask'
import { TransferSingleTask } from '../tasks/TransferSingleTask'
import { TransferTask } from '../tasks/TransferTask'
import { ERROR, ERROR_CODE, RESULT_OK, SUCCESS, delay } from './utils'

export const LIMIT_VALUES: LimitValues = {
  mint: 100,
  burn: 100,
  charge: 100,
  discharge: 100,
  transfer: 100,
  cumulative: {
    total: 3000,
    mint: 500,
    burn: 500,
    charge: 500,
    discharge: 500,
    transfer: 500,
  },
}

const MAX_LIMITS_VALUES: LimitValues = {
  mint: ************,
  burn: ************,
  charge: ************,
  discharge: ************,
  transfer: ************,
  cumulative: {
    total: ***************,
    mint: ***************,
    burn: ***************,
    charge: ***************,
    discharge: ***************,
    transfer: ***************,
  },
}

const requiredFields = [
  'mintLimit',
  'burnLimit',
  'transferLimit',
  'chargeLimit',
  'dischargeLimit',
  'cumulativeLimit',
  'cumulativeAmount',
  'cumulativeDate',
  'cumulativeMintLimit',
  'cumulativeMintAmount',
  'cumulativeBurnLimit',
  'cumulativeBurnAmount',
  'cumulativeChargeLimit',
  'cumulativeChargeAmount',
  'cumulativeDischargeLimit',
  'cumulativeDischargeAmount',
  'cumulativeTransferLimit',
  'cumulativeTransferAmount',
]

async function registerAccount(accountId: string, limitValues?: LimitValues): Promise<void> {
  await new RegisterAccTask(Network.LocalFin).execute({
    accountId,
    limitValues: limitValues ? JSON.stringify(limitValues) : undefined,
  })
}

export async function syncAndActivateBizZoneAccount(network: Network, accountId: string): Promise<void> {
  await new SyncAccountTask(network).execute({ accountId })
  await delay()
  await new SetActiveBusinessAccountWithZoneTask(Network.LocalFin).execute({ accountId })
}

async function checkLimitFields(data: string): Promise<boolean> {
  return requiredFields.every((field) => data.includes(field))
}

async function getLimitValuesAndCompare(accountId: string, expected: LimitValues): Promise<string> {
  const limits = convertToLimits(await new GetAccountLimitDataTask(Network.LocalFin).execute({ accountId }))
  return lodash.isEqual(limits, expected) ? SUCCESS : ERROR
}

function convertToLimits(input: string): LimitValues {
  const result: LimitValues = {
    mint: 0,
    burn: 0,
    charge: 0,
    discharge: 0,
    transfer: 0,
    cumulative: { total: 0, mint: 0, burn: 0, charge: 0, discharge: 0, transfer: 0 },
  }

  input
    .trim()
    .split('\n')
    .forEach((line) => {
      const match = line.match(/^\s*([a-zA-Z]+)\s*\|\s*([^|]*)\s*\|.*$/)
      if (match) {
        const key = match[1].trim()
        const value = parseInt(match[2].trim())
        switch (key) {
          case 'mintLimit':
            result.mint = value
            break
          case 'burnLimit':
            result.burn = value
            break
          case 'chargeLimit':
            result.charge = value
            break
          case 'dischargeLimit':
            result.discharge = value
            break
          case 'transferLimit':
            result.transfer = value
            break
          case 'cumulativeLimit':
            result.cumulative.total = value
            break
          case 'cumulativeMintLimit':
            result.cumulative.mint = value
            break
          case 'cumulativeBurnLimit':
            result.cumulative.burn = value
            break
          case 'cumulativeChargeLimit':
            result.cumulative.charge = value
            break
          case 'cumulativeDischargeLimit':
            result.cumulative.discharge = value
            break
          case 'cumulativeTransferLimit':
            result.cumulative.transfer = value
            break
        }
      }
    })
  return result
}

function convertToCumulativeAmount(input: string): CumulativeAmount {
  const result: CumulativeAmount = {
    cumulativeAmount: 0,
    cumulativeMintAmount: 0,
    cumulativeBurnAmount: 0,
    cumulativeChargeAmount: 0,
    cumulativeDischargeAmount: 0,
    cumulativeTransferAmount: 0,
  }

  input
    .trim()
    .split('\n')
    .forEach((line) => {
      const match = line.match(/^\s*([a-zA-Z]+)\s*\|\s*([^|]*)\s*\|.*$/)
      if (match) {
        const key = match[1].trim()
        result[key] = parseInt(match[2].trim())
      }
    })
  return result
}

// Test Functions
export async function modTokenLimit(
  network: Network,
  accountId: string,
  limitValues: LimitValues = LIMIT_VALUES,
): Promise<string> {
  await new ModTokenLimitTask(network).execute({ accountId, limitValues: JSON.stringify(limitValues) })
  return getLimitValuesAndCompare(accountId, limitValues)
}

export async function getAccountAll(network: Network, accountId: string): Promise<string> {
  const data = await new GetAccountAllTask(network).execute({ accountId })
  return (await checkLimitFields(data)) ? SUCCESS : ERROR
}

export async function getAccountLimit(network: Network, accountId: string): Promise<string> {
  const data = await new GetAccountLimitDataTask(network).execute({ accountId })
  return (await checkLimitFields(data)) ? SUCCESS : ERROR
}

export async function getAccountInfo(network: Network, accountId: string): Promise<string> {
  const data = await new GetAccountInfoTask(network).execute({ accountId })
  return (await checkLimitFields(data)) ? SUCCESS : ERROR
}

export async function addAccountWithoutLimit(network: Network, accountId: string): Promise<string> {
  await registerAccount(accountId)
  return getLimitValuesAndCompare(accountId, MAX_LIMITS_VALUES)
}

export async function addAccountWithLimit(network: Network, accountId: string): Promise<string> {
  await registerAccount(accountId, LIMIT_VALUES)
  return getLimitValuesAndCompare(accountId, LIMIT_VALUES)
}

export async function checkTransactionNotExceedDailyLimit(network: Network, accountId: string): Promise<string> {
  const isBizZone = network !== Network.LocalFin
  await registerAccount(accountId, LIMIT_VALUES)
  isBizZone && (await syncAndActivateBizZoneAccount(network, accountId))

  if ((await getLimitValuesAndCompare(accountId, LIMIT_VALUES)) !== SUCCESS) return ERROR

  await new MintTokenTask(Network.LocalFin).execute({ accountId, amount: '100' })
  if (isBizZone) {
    await new TransferTask(Network.LocalFin).execute({
      accountId,
      toZoneId: networkConfig[network].ZONE_ID,
      amount: '100',
    })
    await delay()
  }

  const checkResult = await new CheckTransactionTask(Network.LocalFin).execute({
    amount: '50',
    sendAccountId: accountId,
    fromAccountId: accountId,
    toAccountId: networkConfig[network].ACCOUNT_ID_2,
    zoneId: networkConfig[network].ZONE_ID,
  })
  return checkResult.includes(RESULT_OK) ? SUCCESS : ERROR
}

export async function checkTransactionNotSetLimit(network: Network, accountId: string): Promise<string> {
  const isBizZone = network !== Network.LocalFin
  await registerAccount(accountId)
  isBizZone && (await syncAndActivateBizZoneAccount(network, accountId))

  if ((await getLimitValuesAndCompare(accountId, MAX_LIMITS_VALUES)) !== SUCCESS) return ERROR

  await new MintTokenTask(Network.LocalFin).execute({ accountId, amount: '10000' })
  if (isBizZone) {
    await new TransferTask(Network.LocalFin).execute({
      accountId,
      toZoneId: networkConfig[network].ZONE_ID,
      amount: '10000',
    })
    await delay()
  }

  const checkResult = await new CheckTransactionTask(Network.LocalFin).execute({
    amount: '10000',
    sendAccountId: accountId,
    fromAccountId: accountId,
    toAccountId: networkConfig[network].ACCOUNT_ID_2,
    zoneId: networkConfig[network].ZONE_ID,
  })
  return checkResult.includes(RESULT_OK) ? SUCCESS : ERROR
}

export async function checkTransactionExceedDailyLimit(network: Network, accountId: string): Promise<string> {
  const isBizZone = network !== Network.LocalFin
  const limit: LimitValues = {
    mint: 200,
    burn: 100,
    charge: 100,
    discharge: 100,
    transfer: 100,
    cumulative: { total: 1000, mint: 200, burn: 100, charge: 100, discharge: 100, transfer: 100 },
  }
  await registerAccount(accountId, limit)
  isBizZone && (await syncAndActivateBizZoneAccount(network, accountId))
  if ((await getLimitValuesAndCompare(accountId, limit)) !== SUCCESS) return ERROR

  await new MintTokenTask(Network.LocalFin).execute({ accountId, amount: '200' })
  if (isBizZone) {
    await new TransferTask(Network.LocalFin).execute({
      accountId,
      toZoneId: networkConfig[network].ZONE_ID,
      amount: '200',
    })
    await delay()
  }
  await new TransferSingleTask(network).execute({
    sendAccountId: accountId,
    fromAccountId: accountId,
    toAccountId: networkConfig[network].ACCOUNT_ID_2,
    amount: '90',
  })
  await delay()
  const checkResult = await new CheckTransactionTask(Network.LocalFin).execute({
    amount: '20',
    sendAccountId: accountId,
    fromAccountId: accountId,
    toAccountId: networkConfig[network].ACCOUNT_ID_2,
    zoneId: networkConfig[network].ZONE_ID,
  })
  return checkResult.includes(ERROR_CODE.EXCEEDED_DAILY_TRANSFER_LIMIT) ? SUCCESS : ERROR
}

export async function checkTransactionFromAndToAccountIssuerAreDifferent(accountId: string): Promise<string> {
  const checkResult = await new CheckTransactionTask(Network.LocalFin).execute({
    sendAccountId: accountId,
    fromAccountId: accountId,
    toAccountId: networkConfig[Network.LocalFin].UNLINKED_ISSUER_ACCOUNT_ID,
  })
  return checkResult.includes(ERROR_CODE.FROM_AND_TO_ACCOUNT_ISSUERS_ARE_DIFFERENT) ? SUCCESS : ERROR
}

export async function checkSync(accountId: string): Promise<string> {
  await registerAccount(accountId, LIMIT_VALUES)
  if ((await getLimitValuesAndCompare(accountId, LIMIT_VALUES)) !== SUCCESS) return ERROR
  await syncAndActivateBizZoneAccount(Network.LocalBiz, accountId)

  console.log('************ Check sync mint ************')
  await new MintTokenTask(Network.LocalFin).execute({ accountId, amount: '90' })
  const afterMintLimitAmount = convertToCumulativeAmount(
    await new GetAccountLimitDataTask(Network.LocalFin).execute({ accountId }),
  )
  if (afterMintLimitAmount.cumulativeMintAmount !== 90) return ERROR

  console.log('************ Check sync burn ************')
  await new BurnTokenTask(Network.LocalFin).execute({ accountId, amount: '10' })
  const afterBurnLimitAmount = convertToCumulativeAmount(
    await new GetAccountLimitDataTask(Network.LocalFin).execute({ accountId }),
  )
  if (afterBurnLimitAmount.cumulativeBurnAmount !== 10) return ERROR

  console.log('************ Check sync charge ************')
  await new TransferTask(Network.LocalFin).execute({
    accountId,
    toZoneId: networkConfig[Network.LocalFin].BIZ_ZONE_ID,
    amount: '10',
  })
  await delay()
  const afterChargeLimitAmount = convertToCumulativeAmount(
    await new GetAccountLimitDataTask(Network.LocalFin).execute({ accountId }),
  )
  if (afterChargeLimitAmount.cumulativeChargeAmount !== 10) return ERROR

  console.log('************ Check sync discharge ************')
  await new TransferTask(Network.LocalBiz).execute({
    accountId,
    toZoneId: networkConfig[Network.LocalFin].ZONE_ID,
    amount: '10',
  })
  await delay()
  const afterDischargeLimitAmount = convertToCumulativeAmount(
    await new GetAccountLimitDataTask(Network.LocalFin).execute({ accountId }),
  )
  if (afterDischargeLimitAmount.cumulativeDischargeAmount !== 10) return ERROR

  console.log('************ Check sync transfer ************')
  await new TransferSingleTask(Network.LocalFin).execute({
    sendAccountId: accountId,
    fromAccountId: accountId,
    toAccountId: networkConfig[Network.LocalFin].ACCOUNT_ID_2,
    amount: '10',
  })

  const afterTransferLimitAmount = convertToCumulativeAmount(
    await new GetAccountLimitDataTask(Network.LocalFin).execute({ accountId }),
  )
  if (afterTransferLimitAmount.cumulativeTransferAmount !== 10) return ERROR

  console.log('************ Cumulative reset ************')
  await new CumulativeResetTask(Network.LocalFin).execute({ accountId })

  const afterResultCumulative = convertToCumulativeAmount(
    await new GetAccountLimitDataTask(Network.LocalFin).execute({ accountId }),
  )
  if (
    afterResultCumulative.cumulativeTransferAmount !== 0 ||
    afterResultCumulative.cumulativeMintAmount !== 0 ||
    afterResultCumulative.cumulativeBurnAmount !== 0 ||
    afterResultCumulative.cumulativeChargeAmount !== 0 ||
    afterResultCumulative.cumulativeDischargeAmount !== 0 ||
    afterResultCumulative.cumulativeAmount !== 0
  ) {
    return ERROR
  }

  return SUCCESS
}

export async function checkTransactionLimit(accountId: string, network: Network): Promise<string> {
  const limit: LimitValues = {
    mint: 200,
    burn: 100,
    charge: 50,
    discharge: 20,
    transfer: 10,
    cumulative: { total: 1000, mint: 300, burn: 150, charge: 80, discharge: 30, transfer: 15 },
  }
  await registerAccount(accountId, limit)
  await syncAndActivateBizZoneAccount(network, accountId)
  if ((await getLimitValuesAndCompare(accountId, limit)) !== SUCCESS) return ERROR
  await new GetAccountAllTask(Network.LocalFin).execute({ accountId })
  console.log('************ Check mint less than daily limit ************')
  const mintCheckOk = await new CheckMintTask(Network.LocalFin).execute({
    accountId,
    amount: '200',
  })
  if (!mintCheckOk.includes(RESULT_OK)) return ERROR

  console.log('************ Check mint exceed daily limit ************')
  await new MintTokenTask(Network.LocalFin).execute({ accountId, amount: '200' })
  const mintCheckExceeded = await new CheckMintTask(Network.LocalFin).execute({
    accountId,
    amount: '200',
  })
  if (!mintCheckExceeded.includes(ERROR_CODE.EXCEEDED_DAILY_MINT_LIMIT)) return ERROR

  console.log('************ Check burn less than daily limit ************')
  await new GetAccountAllTask(Network.LocalFin).execute({ accountId })
  const burnCheckOk = await new CheckBurnTask(Network.LocalFin).execute({
    accountId,
    amount: '100',
  })
  if (!burnCheckOk.includes(RESULT_OK)) return ERROR

  console.log('************ Check burn exceed daily limit ************')
  await new BurnTokenTask(Network.LocalFin).execute({ accountId, amount: '100' })
  const burnCheckExceeded = await new CheckBurnTask(Network.LocalFin).execute({
    accountId,
    amount: '100',
  })
  if (!burnCheckExceeded.includes(ERROR_CODE.EXCEEDED_DAILY_BURN_LIMIT)) return ERROR

  console.log('************ Check charge less than daily limit ************')
  await new GetAccountAllTask(Network.LocalFin).execute({ accountId })
  const chargeCheckOk = await new CheckExchangeTask(Network.LocalFin).execute({
    accountId,
    amount: '50',
    fromZoneId: networkConfig[Network.LocalFin].ZONE_ID,
    toZoneId: networkConfig[Network.LocalBiz].ZONE_ID,
  })
  if (!chargeCheckOk.includes(RESULT_OK)) return ERROR

  console.log('************ Check charge exceed daily limit ************')
  await new TransferTask(Network.LocalFin).execute({
    accountId,
    toZoneId: networkConfig[Network.LocalFin].BIZ_ZONE_ID,
    amount: '50',
  })
  await delay()
  const chargeCheckExceeded = await new CheckExchangeTask(Network.LocalFin).execute({
    accountId,
    amount: '50',
    fromZoneId: networkConfig[Network.LocalFin].ZONE_ID,
    toZoneId: networkConfig[Network.LocalBiz].ZONE_ID,
  })
  if (!chargeCheckExceeded.includes(ERROR_CODE.EXCEEDED_DAILY_CHARGE_LIMIT)) return ERROR

  console.log('************ Check discharge less than daily limit ************')
  await new GetAccountAllTask(Network.LocalFin).execute({ accountId })
  const dischargeCheckOk = await new CheckExchangeTask(Network.LocalFin).execute({
    accountId,
    amount: '20',
    fromZoneId: networkConfig[Network.LocalBiz].ZONE_ID,
    toZoneId: networkConfig[Network.LocalFin].ZONE_ID,
  })
  if (!dischargeCheckOk.includes(RESULT_OK)) return ERROR

  console.log('************ Check discharge exceed daily limit ************')
  await new TransferTask(Network.LocalBiz).execute({
    accountId,
    toZoneId: networkConfig[Network.LocalFin].ZONE_ID,
    amount: '20',
  })
  await delay()
  const dischargeCheckExceeded = await new CheckExchangeTask(Network.LocalFin).execute({
    accountId,
    amount: '20',
    fromZoneId: networkConfig[Network.LocalBiz].ZONE_ID,
    toZoneId: networkConfig[Network.LocalFin].ZONE_ID,
  })
  if (!dischargeCheckExceeded.includes(ERROR_CODE.EXCEEDED_DAILY_DISCHARGE_LIMIT)) return ERROR

  console.log('************ Check transfer less than daily limit ************')
  await new GetAccountAllTask(Network.LocalFin).execute({ accountId })
  const transferCheckOk = await new CheckTransactionTask(Network.LocalFin).execute({
    sendAccountId: accountId,
    fromAccountId: accountId,
    toAccountId: networkConfig[Network.LocalFin].ACCOUNT_ID_2,
    zoneId: networkConfig[Network.LocalFin].ZONE_ID,
    amount: '10',
  })
  if (!transferCheckOk.includes(RESULT_OK)) return ERROR

  console.log('************ Check transfer exceed daily limit ************')
  await new TransferSingleTask(Network.LocalFin).execute({
    sendAccountId: accountId,
    fromAccountId: accountId,
    toAccountId: networkConfig[Network.LocalFin].ACCOUNT_ID_2,
    amount: '10',
  })
  const transferCheckExceeded = await new CheckTransactionTask(Network.LocalFin).execute({
    sendAccountId: accountId,
    fromAccountId: accountId,
    toAccountId: networkConfig[Network.LocalFin].ACCOUNT_ID_2,
    zoneId: networkConfig[Network.LocalFin].ZONE_ID,
    amount: '10',
  })
  if (!transferCheckExceeded.includes(ERROR_CODE.EXCEEDED_DAILY_TRANSFER_LIMIT)) return ERROR

  return SUCCESS
}

export async function checkExchangeNotExceedLimit(network: Network, accountId: string): Promise<string> {
  await registerAccount(accountId, LIMIT_VALUES)
  if ((await getLimitValuesAndCompare(accountId, LIMIT_VALUES)) !== SUCCESS) return ERROR
  await syncAndActivateBizZoneAccount(network, accountId)
  await new MintTokenTask(Network.LocalFin).execute({ accountId, amount: '100' })

  console.log('************ Check exchange does not exceed charge limit ************')
  const checkChargeResult = await new CheckExchangeTask(Network.LocalFin).execute({
    amount: '50',
    accountId,
    fromZoneId: networkConfig[Network.LocalFin].ZONE_ID,
    toZoneId: networkConfig[network].ZONE_ID,
  })
  if (!checkChargeResult.includes(RESULT_OK)) return ERROR
  console.log('************ Check exchange does not exceed discharge limit ************')
  await new TransferTask(Network.LocalFin).execute({
    accountId,
    toZoneId: networkConfig[network].ZONE_ID,
    amount: '100',
  })
  await delay()
  const checkDischargeResult = await new CheckExchangeTask(Network.LocalFin).execute({
    amount: '50',
    accountId,
    fromZoneId: networkConfig[network].ZONE_ID,
    toZoneId: networkConfig[Network.LocalFin].ZONE_ID,
  })
  return checkDischargeResult.includes(RESULT_OK) ? SUCCESS : ERROR
}

export async function checkExchangeNotSetLimit(network: Network, accountId: string): Promise<string> {
  await registerAccount(accountId)
  if ((await getLimitValuesAndCompare(accountId, MAX_LIMITS_VALUES)) !== SUCCESS) return ERROR
  await syncAndActivateBizZoneAccount(network, accountId)
  await new MintTokenTask(Network.LocalFin).execute({ accountId, amount: '10000' })
  console.log('************ Check exchange with no charge limit ************')
  const checkChargeResult = await new CheckExchangeTask(Network.LocalFin).execute({
    amount: '10000',
    accountId,
    fromZoneId: networkConfig[Network.LocalFin].ZONE_ID,
    toZoneId: networkConfig[network].ZONE_ID,
  })
  if (!checkChargeResult.includes(RESULT_OK)) return ERROR

  console.log('************ Check exchange with no discharge limit ************')
  await new TransferTask(Network.LocalFin).execute({
    accountId,
    toZoneId: networkConfig[network].ZONE_ID,
    amount: '10000',
  })
  await delay()
  const checkDischargeResult = await new CheckExchangeTask(Network.LocalFin).execute({
    amount: '10000',
    accountId,
    fromZoneId: networkConfig[network].ZONE_ID,
    toZoneId: networkConfig[Network.LocalFin].ZONE_ID,
  })
  return checkDischargeResult.includes(RESULT_OK) ? SUCCESS : ERROR
}

export async function checkExchangeExceedCumulativeChargeLimit(network: Network, accountId: string): Promise<string> {
  const limit: LimitValues = {
    mint: 200,
    burn: 100,
    charge: 100,
    discharge: 100,
    transfer: 100,
    cumulative: { total: 1000, mint: 200, burn: 100, charge: 100, discharge: 100, transfer: 100 },
  }

  await registerAccount(accountId, limit)
  await syncAndActivateBizZoneAccount(network, accountId)
  await new MintTokenTask(Network.LocalFin).execute({ accountId, amount: '200' })
  await new TransferTask(Network.LocalFin).execute({
    accountId,
    toZoneId: networkConfig[Network.LocalFin].BIZ_ZONE_ID,
    amount: '90',
  })
  await delay()
  const checkResult = await new CheckExchangeTask(Network.LocalFin).execute({
    amount: '20',
    accountId,
    fromZoneId: networkConfig[Network.LocalFin].ZONE_ID,
    toZoneId: networkConfig[network].ZONE_ID,
  })
  return checkResult.includes(ERROR_CODE.EXCEEDED_DAILY_CHARGE_LIMIT) ? SUCCESS : ERROR
}

export async function checkExchangeExceedCumulativeDisChargeLimit(
  network: Network,
  accountId: string,
): Promise<string> {
  const limit: LimitValues = {
    mint: 200,
    burn: 100,
    charge: 200,
    discharge: 100,
    transfer: 100,
    cumulative: { total: 1000, mint: 200, burn: 100, charge: 200, discharge: 100, transfer: 100 },
  }

  await registerAccount(accountId, limit)
  await syncAndActivateBizZoneAccount(network, accountId)
  await new MintTokenTask(Network.LocalFin).execute({ accountId, amount: '200' })
  await new TransferTask(Network.LocalFin).execute({
    accountId,
    toZoneId: networkConfig[network].ZONE_ID,
    amount: '200',
  })
  await delay()
  await new TransferTask(Network.LocalBiz).execute({
    accountId,
    fromZoneId: networkConfig[network].ZONE_ID,
    toZoneId: networkConfig[Network.LocalFin].ZONE_ID,
    amount: '90',
  })
  await delay()
  const checkResult = await new CheckExchangeTask(Network.LocalFin).execute({
    amount: '20',
    accountId,
    fromZoneId: networkConfig[network].ZONE_ID,
    toZoneId: networkConfig[Network.LocalFin].ZONE_ID,
  })
  return checkResult.includes(ERROR_CODE.EXCEEDED_DAILY_DISCHARGE_LIMIT) ? SUCCESS : ERROR
}
