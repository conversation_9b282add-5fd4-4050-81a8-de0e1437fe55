import { commonConfig, Network, networkConfig } from '../helpers/constants'
import { LocalFinConfig } from '../helpers/types'
import { GetAccountAllTask } from '../tasks/GetAccountAllTask'
import { SetBizZoneTerminatedTask } from '../tasks/SetBizZoneTerminatedTask'
import { ERROR, extractAccountInfo, showAccountsStatus, SUCCESS } from './utils'

async function processBizTerminated(network: Network.LocalFin, accountId: string) {
  const getAccountAllTask = new GetAccountAllTask(network)
  const checkResult = await getAccountAllTask.execute({
    accountId,
    validId: networkConfig[network].VALID_ID,
  })

  if (checkResult && !checkResult?.includes('Not linked from Business Zone.')) {
    if (extractAccountInfo(checkResult).finZone.bizZoneAccountStatus !== commonConfig.STATUS_TERMINATING)
      throw new Error(`BZ account ${accountId} is not in terminating status.`)

    const setBizZoneTerminatedTask = new SetBizZoneTerminatedTask(network)
    return await setBizZoneTerminatedTask.execute({
      accountId,
      zoneId: (networkConfig[network] as LocalFinConfig).BIZ_ZONE_ID,
    })
  } else {
    throw new Error('BZ account is not linked.')
  }
}

async function bizTerminated(network: Network.LocalFin) {
  const config = networkConfig[network]

  const processResult1 = await processBizTerminated(network, config.ACCOUNT_ID_3)
  if (!processResult1.includes(SUCCESS)) return ERROR
  const processResult2 = await processBizTerminated(network, config.ACCOUNT_ID_4)
  if (!processResult2.includes(SUCCESS)) return ERROR

  const showStatus1 = extractAccountInfo(await showAccountsStatus(config.ACCOUNT_ID_3))
  if (
    showStatus1.finZone.status !== commonConfig.STATUS_ACTIVE ||
    showStatus1.finZone.bizZoneAccountStatus !== commonConfig.STATUS_TERMINATED ||
    showStatus1.bizZone.status !== commonConfig.STATUS_TERMINATED
  ) {
    return ERROR
  }

  const showStatus2 = extractAccountInfo(await showAccountsStatus(config.ACCOUNT_ID_4))
  if (
    showStatus2.finZone.status !== commonConfig.STATUS_ACTIVE ||
    showStatus2.finZone.bizZoneAccountStatus !== commonConfig.STATUS_TERMINATED ||
    showStatus2.bizZone.status !== commonConfig.STATUS_TERMINATED
  ) {
    return ERROR
  }
  return SUCCESS
}

export { bizTerminated }
